{"root": true, "extends": ["next/core-web-vitals", "plugin:prettier/recommended", "plugin:tailwindcss/recommended", "plugin:@tanstack/query/recommended"], "rules": {"react-hooks/exhaustive-deps": "off", "@next/next/no-img-element": "off", "import/no-anonymous-default-export": "off", "tailwindcss/no-custom-classname": "off"}, "overrides": [{"files": ["*.ts", "*.tsx", "*.js"], "parser": "@typescript-eslint/parser"}]}