{"name": "next14-tailwind", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "dev:turbo": "next dev --turbo", "build": "next build", "start": "next start", "start:static": "npx serve@latest out", "lint:check": "next lint", "lint:fix": "next lint --fix", "prepare": "husky"}, "dependencies": {"@hookstate/core": "^4.0.1", "@hookstate/devtools": "^4.0.3", "@hookstate/localstored": "^4.0.2", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@rainbow-me/rainbowkit": "^2.2.4", "@sky-mavis/tanto-wagmi": "^0.0.7", "@tanstack/react-query": "^5.68.0", "@tanstack/react-query-devtools": "^5.68.0", "axios": "^1.8.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^3.6.0", "geist": "^1.3.1", "lodash.merge": "^4.6.2", "lucide-react": "^0.482.0", "next": "14.2.23", "next-themes": "^0.4.6", "react": "18.3.1", "react-day-picker": "8.10.1", "react-dom": "18.3.1", "react-icons": "^5.5.0", "sonner": "^1.7.4", "tailwind-merge": "^2.6.0", "tailwindcss": "3.4.17", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.2", "viem": "2.x", "wagmi": "^2.14.13"}, "devDependencies": {"@tanstack/eslint-plugin-query": "^5.68.0", "@types/lodash.merge": "^4.6.9", "@types/node": "22.13.10", "@types/react": "18.3.8", "@types/react-dom": "18.3.0", "@typescript-eslint/parser": "^7.6.0", "autoprefixer": "10.4.21", "eslint": "8.57.0", "eslint-config-next": "14.2.23", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-tailwindcss": "^3.18.0", "husky": "^9.1.7", "postcss": "8.5.3", "prettier": "^3.5.3", "typescript": "5.5.4"}}