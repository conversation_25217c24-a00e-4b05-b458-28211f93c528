'use client';

import { useEffect, useRef } from 'react';
import styles from './Game.module.css';

// Extend the Window interface to include Unity properties
declare global {
  interface Window {
    createUnityInstance: (
      canvas: HTMLElement | null,
      config: {
        dataUrl: string;
        frameworkUrl: string;
        codeUrl: string;
      },
    ) => Promise<any>;
    unityInstance?: any;
  }
}

export default function Game() {
  const canvasRef = useRef<HTMLCanvasElement>(null);

  useEffect(() => {
    const loadUnity = () => {
      const script = document.createElement('script');
      script.src = '/unity/Build/Build.loader.js';
      script.onload = () => {
        const canvas = canvasRef.current;
        if (!canvas) return;

        // Set fixed canvas size to prevent FBO recreation
        canvas.width = 960;
        canvas.height = 600;
        canvas.style.width = '960px';
        canvas.style.height = '600px';

        window
          .createUnityInstance(canvas, {
            dataUrl: '/unity/Build/Build.data',
            frameworkUrl: '/unity/Build/Build.framework.js',
            codeUrl: '/unity/Build/Build.wasm',
          })
          .then((unityInstance) => {
            window.unityInstance = unityInstance;

            // Disable automatic canvas resizing
            if (unityInstance.Module && unityInstance.Module.canvas) {
              unityInstance.Module.canvas.style.width = '960px';
              unityInstance.Module.canvas.style.height = '600px';
            }
          })
          .catch((err) => {
            console.error('Unity failed to load:', err);
          });
      };
      document.body.appendChild(script);
    };

    loadUnity();

    // Cleanup function
    return () => {
      if (window.unityInstance) {
        window.unityInstance.Quit();
        window.unityInstance = undefined;
      }
    };
  }, []);

  return (
    <div className={styles.gameContainer}>
      <canvas
        ref={canvasRef}
        id="unity-canvas"
        width={960}
        height={600}
        className={styles.unityCanvas}
        style={{ width: '960px', height: '600px' }}
      />
    </div>
  );
}
