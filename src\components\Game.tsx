'use client';

import { useEffect } from 'react';

export default function Game() {
  useEffect(() => {
    const loadUnity = () => {
      const script = document.createElement('script');
      script.src = '/unity/Build/build.loader.js'; // Match your actual file name
      script.onload = () => {
        // @ts-ignore
        window
          .createUnityInstance(document.getElementById('unity-canvas'), {
            dataUrl: '/unity/Build/build.data',
            frameworkUrl: '/unity/Build/build.framework.js',
            codeUrl: '/unity/Build/build.wasm',
          })
          .then((unityInstance) => {
            // @ts-ignore
            window.unityInstance = unityInstance;
          })
          .catch((err) => {
            console.error('Unity failed to load:', err);
          });
      };
      document.body.appendChild(script);
    };

    loadUnity();
  }, []);

  return (
    <div className="flex min-h-screen items-center justify-center bg-gray-100">
      <canvas id="unity-canvas" width="960" height="600" className="rounded-lg border" />
    </div>
  );
}
